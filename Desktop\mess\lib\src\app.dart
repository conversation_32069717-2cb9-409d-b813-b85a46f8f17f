import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// This file will contain the main app widget and routing logic
// Currently exported from main.dart, will be expanded in UI development phase

class AppRouter {
  static const String splash = '/';
  static const String auth = '/auth';
  static const String home = '/home';
  static const String networkSelection = '/network-selection';
  static const String deviceDiscovery = '/device-discovery';
  static const String chat = '/chat';
  static const String settings = '/settings';
}

// Route definitions will be implemented in the UI development phase
final routerProvider = Provider<Map<String, WidgetBuilder>>((ref) {
  return {
    AppRouter.splash: (context) => const SplashScreen(),
    AppRouter.auth: (context) => const AuthScreen(),
    AppRouter.home: (context) => const MainNavigationScreen(),
    AppRouter.networkSelection: (context) => const NetworkSelectionScreen(),
    AppRouter.deviceDiscovery: (context) => const DeviceDiscoveryScreen(),
    AppRouter.chat: (context) => const ChatScreen(),
    AppRouter.settings: (context) => const SettingsScreen(),
  };
});

// Placeholder screens - will be implemented in UI development phase
class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}

class AuthScreen extends StatelessWidget {
  const AuthScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(
        child: Text('Authentication Screen'),
      ),
    );
  }
}

class MainNavigationScreen extends StatelessWidget {
  const MainNavigationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(
        child: Text('Main Navigation'),
      ),
    );
  }
}

class NetworkSelectionScreen extends StatelessWidget {
  const NetworkSelectionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(
        child: Text('Network Selection'),
      ),
    );
  }
}

class DeviceDiscoveryScreen extends StatelessWidget {
  const DeviceDiscoveryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(
        child: Text('Device Discovery'),
      ),
    );
  }
}

class ChatScreen extends StatelessWidget {
  const ChatScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(
        child: Text('Chat Screen'),
      ),
    );
  }
}

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(
        child: Text('Settings Screen'),
      ),
    );
  }
}
